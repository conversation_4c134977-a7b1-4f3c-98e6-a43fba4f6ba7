using UnrealBuildTool;

public class SageCommonTypes : ModuleRules
{
    public SageCommonTypes(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "Json",
            }
        );
        
        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",
            }
        );
        
        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                }
            );
        }
    }
}